import { BaseSearchDto } from '@core/support/validation/base-search-dto';
import { IsOptional, IsLatitude, IsLongitude, IsIn } from 'class-validator';

const orderByIn = ['createdAt', 'updatedAt', 'timer_hours', 'name', 'last_activity'] as const;
export class SearchVibeDto extends BaseSearchDto {
  @IsLatitude()
  @IsOptional()
  lat: string;

  @IsLongitude()
  @IsOptional()
  long: string;

  @IsOptional()
  @IsIn(orderByIn)
  orderBy: typeof orderByIn[number] = 'updatedAt';
}
