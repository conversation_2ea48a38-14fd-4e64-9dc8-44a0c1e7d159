import { Test, TestingModule } from '@nestjs/testing';
import { VibeService } from '@module/vibes/service/vibes.service';
import { CreateVibeDto } from '@module/vibes/validation/create-vibe.dto';
import { SearchVibeDto } from '@module/vibes/validation/search-vibe.dto';

describe('VibeService', () => {
  let vibeService: VibeService;

  const mockVibeService = {
    createVibe: jest.fn(),
    getVibes: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: VibeService,
          useValue: mockVibeService // Use the mock implementation
        }
      ]
    }).compile();

    vibeService = module.get<VibeService>(VibeService);
  });

  it('should be defined', () => {
    expect(vibeService).toBeDefined();
  });

  describe('createVibe', () => {
    it('should create vibe', async () => {
      const newVibe = new CreateVibeDto();
      newVibe.name = 'Test Vibe';
      newVibe.description = 'Description of test vibe';
      newVibe.tagId = '234cad76-13d1-40f4-856b-1133bbc89657';
      const createdBy = '233b7c8f-b505-4c31-b052-6ccdc21da673';

      const mockedResultValue = {
        ...newVibe,
        createdBy,
        updatedBy: createdBy,
        id: '9566f752-4dca-4751-836d-e64a238cb925'
      };

      mockVibeService.createVibe.mockResolvedValue(mockedResultValue);

      const result = await vibeService.createVibe({ ...newVibe, createdBy, updatedBy: createdBy });

      expect(mockVibeService.createVibe).toHaveBeenCalled();
      expect(result).toEqual(mockedResultValue);
    });

    it('should create vibe with timer hours and calculate expiredAt', async () => {
      const newVibe = new CreateVibeDto();
      newVibe.name = 'Test Vibe with Timer';
      newVibe.description = 'Description of test vibe';
      newVibe.tagId = '234cad76-13d1-40f4-856b-1133bbc89657';
      newVibe.timerHours = 2;
      const createdBy = '233b7c8f-b505-4c31-b052-6ccdc21da673';

      const mockedResultValue = {
        ...newVibe,
        createdBy,
        updatedBy: createdBy,
        id: '9566f752-4dca-4751-836d-e64a238cb925',
        expiredAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
      };

      mockVibeService.createVibe.mockResolvedValue(mockedResultValue);

      const result = await vibeService.createVibe({ ...newVibe, createdBy, updatedBy: createdBy });

      expect(mockVibeService.createVibe).toHaveBeenCalled();
      expect(result).toEqual(mockedResultValue);
    });
  });

  describe('getVibes', () => {
    it('should get list vibes', async () => {
      const searchVibeDto = new SearchVibeDto();

      const mockedResultValue = [
        {
          name: 'Test Vibe',
          description: 'Description of test vibe',
          tagId: '234cad76-13d1-40f4-856b-1133bbc89657',
          createdBy: '233b7c8f-b505-4c31-b052-6ccdc21da673',
          updatedBy: '233b7c8f-b505-4c31-b052-6ccdc21da673',
          createdAt: new Date(),
          updatedAt: new Date(),
          id: '9566f752-4dca-4751-836d-e64a238cb925'
        }
      ];

      mockVibeService.getVibes.mockResolvedValue(mockedResultValue);

      const result = await vibeService.getVibes(searchVibeDto);

      expect(mockVibeService.getVibes).toHaveBeenCalled();
      expect(result).toEqual(mockedResultValue);
    });
  });
});
