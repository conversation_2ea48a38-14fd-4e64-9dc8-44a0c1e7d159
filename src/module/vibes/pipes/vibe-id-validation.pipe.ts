import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { VibeEntity } from '@module/vibes/entity/vibe.entity';
import { VIBE_STATUS } from '@module/vibes/const/status';

@Injectable()
export class VibeIdValidationPipe implements PipeTransform {
  async transform(vibeId: string) {
    const vibe = await VibeEntity.createQueryBuilder('vibe')
      .where('vibe.id = :vibeId', { vibeId })
      .andWhere('vibe.status = :status', { status: VIBE_STATUS.active })
      .andWhere('(vibe.expiredAt IS NULL OR vibe.expiredAt > :currentTime)', {
        currentTime: new Date()
      })
      .getOne();

    if (!vibe) {
      throw new BadRequestException('VIBE001');
    }

    return vibe;
  }
}
