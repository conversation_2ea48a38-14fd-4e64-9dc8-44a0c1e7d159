import {
  // BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { IsUserGuard } from '@core/auth/guard/is-user.guard';
import { VibeService } from '@module/vibes/service/vibes.service';
import { SearchVibeDto } from '@module/vibes/validation/search-vibe.dto';
import { UserEntity } from '@core/user/entity/user.entity';
import { AuthUser } from '@core/auth/decorator/request-user.decorator';
import { CreateVibeDto } from '@module/vibes/validation/create-vibe.dto';
import { JoinVibeDto } from '@module/vibes/validation/join-vibe.dto';
import { VIBE_TYPE } from '@module/vibes/const';
import { VibeUserService } from '@module/vibes/service/vibe-user.service';
import { VibeIdValidationPipe } from '@module/vibes/pipes/vibe-id-validation.pipe';
// import { SendMessageVibeDto } from '@module/vibes/validation/send-message-vibe.dto';
import { ModerationService } from '@module/moderation/service/moderation.service';
import { DatabaseTransactionService } from '@core/database/support/transaction';
import { SearchVibeMemberDto } from '@module/vibes/validation/search-vibe-member.dto';
// import { VibeStatisticEntity } from '@module/vibes/entity/vibe-statistic.entity';

@UseGuards(AuthGuard(), IsUserGuard)
@Controller('vibes')
export class VibeController {
  constructor(
    private readonly vibeService: VibeService,
    private readonly vibeUserService: VibeUserService,
    private readonly moderationService: ModerationService,
    private readonly databaseTransactionService: DatabaseTransactionService
  ) {}

  @Get('/')
  getListVibes(@Query() searchVibeDto: SearchVibeDto, @AuthUser() user: UserEntity) {
    return this.vibeService.getVibes(searchVibeDto, user);
  }

  @Post('/')
  async createVibe(@Body() createVibeDto: CreateVibeDto, @AuthUser() user: UserEntity) {
    await createVibeDto.extraValidation();

    return this.vibeService.createVibe({
      ...createVibeDto,
      type: createVibeDto.type || VIBE_TYPE.public,
      createdBy: user.id,
      updatedBy: user.id
    });
  }

  @Post('/:vibeId/join')
  async joinVibe(
    @Param('vibeId', ParseUUIDPipe, VibeIdValidationPipe) vibe,
    @Body() joinVibeDto: JoinVibeDto,
    @AuthUser() user: UserEntity
  ) {
    // Check proximity access if proximity lock is enabled
    await this.vibeService.checkProximityAccess(vibe, joinVibeDto.location);

    return this.vibeUserService.updateLastActivity(user.id, { vibeId: vibe.id });
  }

  @Get('/:vibeId/members')
  async getVibeMembers(
    @Param('vibeId', ParseUUIDPipe, VibeIdValidationPipe) vibe,
    @Query() searchVibeMemberDto: SearchVibeMemberDto
  ) {
    return this.vibeUserService.getMembersOfVibe(vibe.id, searchVibeMemberDto);
  }

  // @Post('/:vibeId/send-message')
  // async sendMessage(
  //   @Param('vibeId', ParseUUIDPipe, VibeIdValidationPipe) vibe,
  //   @Body() sendMessageVibeDto: SendMessageVibeDto,
  //   @AuthUser() user: UserEntity
  // ) {
  //   const resultModeration = await this.moderationService.checkUserContentMalicious(
  //     {
  //       field: 'message',
  //       content: sendMessageVibeDto.message,
  //       action: 'userSendMessageInVibe'
  //     },
  //     user
  //   );

  //   if (resultModeration) {
  //     throw new BadRequestException(
  //       this.moderationService.getHumanReadableMaliciousMessage(resultModeration.maliciousContents)
  //     );
  //   }

  //   if (sendMessageVibeDto.photos?.length) {
  //     await Promise.all(
  //       sendMessageVibeDto.photos.map(async photo => {
  //         const resultModerationPhoto = await this.moderationService.checkUserImageMalicious(
  //           {
  //             field: 'photos',
  //             image: photo,
  //             action: 'userSendMessageInVibe'
  //           },
  //           user
  //         );
  //         if (resultModerationPhoto) {
  //           throw new BadRequestException(
  //             this.moderationService.getHumanReadableMaliciousMessage(resultModerationPhoto.maliciousCategoryNames)
  //           );
  //         }
  //       })
  //     );
  //   }

  //   this.databaseTransactionService.run(async entityManager => {
  //     // Update last activity in vibe user table
  //     await this.vibeUserService.updateLastActivity(
  //       user.id,
  //       {
  //         userId: user.id,
  //         vibeId: vibe.id
  //       },
  //       entityManager
  //     );

  //     await VibeStatisticEntity.updateOrCreateV1(
  //       {
  //         vibeId: vibe.id
  //       },
  //       {
  //         create: {
  //           vibeId: vibe.id,
  //           lastMessageAt: new Date(),
  //           lastVisitedAt: new Date()
  //         },
  //         update: {
  //           lastMessageAt: new Date(),
  //           lastVisitedAt: new Date()
  //         }
  //       },
  //       entityManager
  //     );
  //   });
  // }
}
