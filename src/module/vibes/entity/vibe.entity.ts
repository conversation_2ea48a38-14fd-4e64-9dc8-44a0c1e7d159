import {
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON>umn,
  DeleteDateC<PERSON>umn,
  Entity,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import { UserEntity } from '@core/user/entity/user.entity';
import BaseEntity from '@core/database/entity/base-entity';
import { Exclude } from 'class-transformer';
import { STRING_LENGTH, STRING_LONG_LENGTH } from '@core/database/schema/column';
import { VIBE_STATUS } from '@module/vibes/const/status';
import { VIBE_TYPE } from '@module/vibes/const';
import { TagEntity } from '@module/tag/entity/tag.entity';
import { PointTransformer, Point } from '@core/database/type/point';
import { VibeCommentEntity } from '@module/vibes/entity/vibe-comment.entity';

@Entity('vibes')
export class VibeEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: STRING_LENGTH })
  name: string;

  @Column({ length: STRING_LONG_LENGTH })
  description: string;

  @Column({ enum: VIBE_STATUS, default: VIBE_STATUS.active })
  status: VIBE_STATUS;

  @Column({ enum: VIBE_TYPE })
  type: VIBE_TYPE;

  @Column({ name: 'tag_id', type: 'uuid' })
  tagId: string;

  @Column({ type: 'point', transformer: new PointTransformer() })
  location: Point;

  @Column({ name: 'expired_at' })
  expiredAt: Date;

  @Column({ name: 'timer_hours', type: 'int', nullable: true, comment: 'Timer duration in hours (2, 4, 8, etc.)' })
  timerHours: number;

  @Column({
    name: 'proximity_lock',
    type: 'boolean',
    default: false,
    comment: 'Whether to restrict joining based on location proximity'
  })
  proximityLock: boolean;

  @Column({
    name: 'proximity_radius_km',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Radius in kilometers for proximity lock'
  })
  proximityRadiusKm: number;

  @Column({ name: 'is_pin', type: 'boolean', default: false, comment: 'Whether this vibe is pinned by admin' })
  isPin: boolean;

  @Column({
    name: 'pin_order',
    type: 'int',
    nullable: true,
    comment: 'Order of pinned vibes (lower numbers appear first)'
  })
  pinOrder: number;

  @Column({ name: 'created_by', type: 'uuid' })
  createdBy: string;

  @Column({ name: 'updated_by', type: 'uuid' })
  updatedBy: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  @Exclude({ toPlainOnly: true })
  deletedAt?: Date;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @ManyToOne(() => TagEntity)
  @JoinColumn({ name: 'tag_id' })
  tag: TagEntity;

  @Column({ name: 'distance', select: false, insert: false, update: false })
  distance: number;

  @OneToMany(() => VibeCommentEntity, comment => comment.vibe)
  vibeComments: VibeCommentEntity[];
}
