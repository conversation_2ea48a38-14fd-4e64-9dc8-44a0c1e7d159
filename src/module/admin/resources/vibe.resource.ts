import { ValidationError, flatten, unflatten } from 'adminjs';
import { VibeEntity } from '@module/vibes/entity/vibe.entity';
import { VIBE_TYPE } from '@module/vibes/const';
import { handleResponseUser } from './utils/handle-response-user-info';

const validateNewAndEditActionVoucher = async request => {
  const payloadUnflatten = unflatten(request.payload);

  if (!payloadUnflatten.name) {
    throw new ValidationError(
      {
        name: {
          message: 'Name is required'
        }
      },
      {
        message: 'Name is required'
      }
    );
  }

  if (!payloadUnflatten.status) {
    throw new ValidationError(
      {
        status: {
          message: 'Status is required'
        }
      },
      {
        message: 'Status is required'
      }
    );
  }

  request.payload = flatten(payloadUnflatten);

  return request;
};

export const vibeResource = {
  resource: VibeEntity,
  options: {
    navigation: {
      name: 'Vibe Management'
    },
    sort: {
      direction: 'desc',
      sortBy: 'createdAt'
    },
    filterProperties: ['name', 'tagId', 'createdBy', 'createdAt'],
    listProperties: ['name', 'description', 'tagId', 'status', 'timerHours', 'proximityLock', 'createdBy', 'createdAt', 'updatedAt'],
    showProperties: ['name', 'description', 'tagId', 'status', 'location', 'timerHours', 'proximityLock', 'proximityRadiusKm', 'createdBy', 'createdAt', 'updatedAt'],
    editProperties: ['name', 'description', 'tagId', 'status', 'location', 'timerHours', 'proximityLock', 'proximityRadiusKm'],
    properties: {
      tagId: {
        reference: 'TagEntity',
        isRequired: false
      },
      name: {
        type: 'string',
        isRequired: true
      },
      description: {
        type: 'textarea'
      },
      status: {
        type: 'string',
        isRequired: true
      },
      location: {
        type: 'mixed'
      },
      'location.lat': {
        type: 'string'
      },
      'location.long': {
        type: 'string'
      },
      timerHours: {
        type: 'number',
        description: 'Timer duration in hours (2, 4, 8, etc.)',
        availableValues: [
          { value: 2, label: '2 hours' },
          { value: 4, label: '4 hours' },
          { value: 8, label: '8 hours' },
          { value: 12, label: '12 hours' },
          { value: 24, label: '24 hours' }
        ]
      },
      proximityLock: {
        type: 'boolean',
        description: 'Restrict joining based on location proximity'
      },
      proximityRadiusKm: {
        type: 'number',
        description: 'Radius in kilometers for proximity lock (only when proximity lock is enabled)'
      }
    },
    actions: {
      new: {
        before: async req => {
          const { session } = req;
          req = await validateNewAndEditActionVoucher(req);
          req.payload.type = VIBE_TYPE.public;
          req.payload.createdBy = req.payload?.createdBy ? req.payload.createdBy : session?.adminUser?.id;
          req.payload.updatedBy = req.payload.createdBy;

          return req;
        }
      },
      edit: {
        isVisible: true,
        before: async req => {
          if (String(req.method).toLowerCase() === 'post') {
            const { session } = req;
            req = await validateNewAndEditActionVoucher(req);
            if (session?.adminUser?.id) {
              req.payload.updatedBy = session.adminUser.id;
            }
          }

          return req;
        }
      },
      list: {
        isVisible: true,
        before: req => {
          return req;
        },
        after: res => {
          if (res.records?.length) {
            res.records = res.records.map(r => {
              if (typeof r.populated?.tagId === 'string') {
                r.populated.tagId = {
                  id: r.populated?.tagId,
                  recordActions: []
                };
              }

              if (r.populated?.tagId?.params) {
                r.populated.tagId.title = r.populated?.tagId?.params?.tag || r?.populated?.params?.tag;
              }

              if (typeof r.populated?.createdBy === 'string') {
                r.populated.createdBy = {
                  id: r.populated?.createdBy,
                  recordActions: []
                };
              } else {
                r.populated.createdBy = handleResponseUser(r.populated.createdBy);
              }

              return r;
            });
          }

          return res;
        }
      },
      show: {
        isVisible: true,
        isAccessible: true,
        after: res => {
          if (res.record.populated?.tagId?.params) {
            res.record.populated.tagId.title =
              res.record.populated?.tagId?.params?.tag || res.record?.populated?.params?.tag;
          }
          if (typeof res.record.populated?.createdBy === 'string') {
            res.record.populated.createdBy = {
              id: res.record.populated?.createdBy,
              recordActions: []
            };
          } else {
            res.record.populated.createdBy = handleResponseUser(res.record.populated.createdBy);
          }

          return res;
        }
      }
    }
  }
};
